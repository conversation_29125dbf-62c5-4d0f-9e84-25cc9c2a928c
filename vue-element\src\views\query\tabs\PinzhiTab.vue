<template>
  <div class="pinzhi-tab">
    <!-- 数据表格 -->
    <el-card class="data-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>品智收银数据</h4>
          <div class="table-actions">
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="success" size="small" @click="showAIAnalysis">
              <el-icon><ChatDotSquare /></el-icon>
              AI分析
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="tableData" stripe v-loading="loading" border class="pinzhi-table" :fit="true">
        <el-table-column label="字段名称" prop="fieldName" min-width="180" align="center">
          <template #default="scope">
            <div class="field-name">
              <span>{{ scope.row.fieldName }}</span>
              <el-tooltip :content="scope.row.calculationMethod" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="actualValue" label="实际值" min-width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.actualValue !== undefined && scope.row.actualValue !== null">{{ formatValue(scope.row.actualValue, scope.row.fieldName) }}{{ scope.row.unit }}</span>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
        <el-table-column label="环比对比" min-width="180" align="center">
          <template #header>
            <div class="column-header">
              <span>环比对比</span>
              <el-tooltip content="❗历史数据变化" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            <div class="comparison-cell" v-if="scope.row.chainComparison && scope.row.chainComparison.length > 0">
              <div class="comparison-row">
                <span :class="['comparison-value', getChangeClass(scope.row.chainChangeRate[0])]">
                  {{ formatValue(scope.row.chainComparison[0], scope.row.fieldName) }}{{ scope.row.unit }}
                  <small v-if="scope.row.chainChangeRate[0] !== '-'">
                    ({{ scope.row.chainChangeRate[0] }})
                  </small>
                </span>
              </div>
            </div>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
        <el-table-column label="同比对比" min-width="180" align="center">
          <template #header>
            <div class="column-header">
              <span>同比对比</span>
              <el-tooltip content="❗与去年同期数据对比" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            <div class="comparison-cell" v-if="scope.row.yearOverYear !== undefined && scope.row.yearOverYear !== null">
              <div class="comparison-row">
                <span :class="['comparison-value', getChangeClass(scope.row.yearOverYearRate)]">
                  {{ formatValue(scope.row.yearOverYear, scope.row.fieldName) }}{{ scope.row.unit }}
                  <small v-if="scope.row.yearOverYearRate !== '-'">
                    ({{ scope.row.yearOverYearRate }})
                  </small>
                </span>
              </div>
            </div>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- AI分析结果 -->
    <el-card v-if="showAIResult" class="ai-analysis-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>
            <el-icon><ChatDotSquare /></el-icon>
            AI智能分析
          </h4>
          <el-button type="text" size="small" @click="showAIResult = false">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
      <div class="ai-analysis-content">
        <el-alert title="分析结果" type="info" :closable="false">
          <div class="analysis-text">
            {{ aiAnalysisResult }}
          </div>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, ChatDotSquare, Close, InfoFilled } from '@element-plus/icons-vue'
import { PINZHI_CASHIER_MODULE } from '@/utils/constant'
import { memberReportApi } from '@/api/index'

export default {
  name: 'PinzhiTab',
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update-loading'],
  setup(props, { emit }) {
    const showAIResult = ref(false)
    const aiAnalysisResult = ref('')
    const loading = ref(false)
    const pinzhiCashierData = ref({})

    // 获取品智收银数据
    const fetchPinzhiCashierData = async () => {
      if (!props.queryParams || !props.queryParams.bid) {
        console.log('PinzhiTab - 缺少必要参数，跳过查询')
        return
      }

      // 检查是否为品智收银系统
      if (props.queryParams.cashier_system !== '1') {
        console.log('PinzhiTab - 非品智收银系统，跳过查询')
        return
      }

      // 检查是否有商户ID
      if (!props.queryParams.merchant_id) {
        console.log('PinzhiTab - 缺少商户ID，跳过查询')
        return
      }

      loading.value = true
      emit('update-loading', true)

      // 🔥 显示大数据量查询提示
      ElMessage.info('正在查询品智收银数据，数据量较大可能需要1-2分钟，请耐心等待...')

      try {
        // 调用真实API获取品智收银数据
        console.log('PinzhiTab - 查询参数:', props.queryParams)
        console.log('PinzhiTab - 收银系统:', props.queryParams.cashier_system)
        console.log('PinzhiTab - 商户ID:', props.queryParams.merchant_id)

        // 调用后端API
        const response = await memberReportApi.getPinzhiCashierData(props.queryParams)

        console.log('PinzhiTab - API响应:', response)

        // 与其他tab保持一致的处理方式
        pinzhiCashierData.value = response.data || {}
        console.log('PinzhiTab - 获取到的数据:', pinzhiCashierData.value)

      } catch (error) {
        console.error('获取品智收银数据失败:', error)
        console.error('错误详情:', error.response?.data || error.message)
        ElMessage.error(`获取品智收银数据失败: ${error.response?.data?.detail || error.message}`)
        pinzhiCashierData.value = {}
      } finally {
        loading.value = false
        emit('update-loading', false)
      }
    }

    // 监听查询参数变化
    watch(() => props.queryParams, (newParams) => {
      if (newParams && newParams.bid) {
        fetchPinzhiCashierData()
      }
    }, { deep: true, immediate: true })

    // 表格数据
    const tableData = computed(() => {
      // 当没有数据时，显示所有字段但标记为"未查询到当前数据"
      if (!pinzhiCashierData.value || Object.keys(pinzhiCashierData.value).length === 0) {
        return Object.entries(PINZHI_CASHIER_MODULE).map(([fieldName, fieldConfig]) => {
          return {
            fieldName,
            actualValue: null, // 设置为null以触发"未查询到当前数据"显示
            unit: '',
            chainComparison: [],
            chainChangeRate: [],
            chainLabels: [],
            yearOverYear: null,
            yearOverYearRate: '-',
            calculationMethod: fieldConfig.logic || ''
          }
        })
      }

      console.log('PinzhiTab - 计算表格数据，当前数据:', pinzhiCashierData.value)

      return Object.entries(PINZHI_CASHIER_MODULE).map(([fieldName, fieldConfig]) => {
        const key = fieldConfig.key
        const itemData = pinzhiCashierData.value[key] || {}

        console.log(`PinzhiTab - 字段 ${fieldName}(${key}):`, itemData)

        return {
          fieldName,
          actualValue: itemData.value !== undefined ? itemData.value : null,
          unit: itemData.unit || '',
          chainComparison: itemData.chainComparison || [],
          chainChangeRate: itemData.chainChangeRate || [],
          chainLabels: itemData.chainLabels || [],
          yearOverYear: itemData.yearOverYear !== undefined ? itemData.yearOverYear : null,
          yearOverYearRate: itemData.yearOverYearRate || '-',
          calculationMethod: fieldConfig.logic || ''
        }
      })
    })

    // 格式化数值
    const formatValue = (value, fieldName) => {
      if (typeof value === 'number') {
        // 百分比字段特殊处理（后端已经转换为百分比格式，直接保留2位小数）
        if (fieldName && (fieldName.includes('占比') || fieldName.includes('折扣率'))) {
          return value.toFixed(2)
        }
        // 客单价保留2位小数
        if (fieldName && fieldName.includes('客单价')) {
          return value.toFixed(2)
        }
        // 其他数值使用千分位分隔
        return value.toLocaleString()
      }
      return value
    }

    // 获取变化样式类
    const getChangeClass = (rate) => {
      if (rate === '-' || rate === '0%' || rate === '0.00%') return 'no-change'
      if (rate.includes('-')) return 'decrease'
      return 'increase'
    }

    // 导出数据
    const exportData = () => {
      ElMessage.success('数据导出功能开发中...')
    }

    // 显示AI分析
    const showAIAnalysis = async () => {
      if (!props.queryParams || !props.queryParams.bid) {
        ElMessage.warning('请先设置查询条件')
        return
      }

      showAIResult.value = true
      aiAnalysisResult.value = '正在分析中，请稍候...'

      try {
        // 调用后端AI分析API
        const response = await memberReportApi.getPinzhiCashierAIAnalysis(props.queryParams)

        if (response && response.code === 200) {
          aiAnalysisResult.value = response.data || '暂无分析结果'
        } else {
          aiAnalysisResult.value = response.message || 'AI分析失败'
        }
      } catch (error) {
        console.error('获取AI分析失败:', error)
        aiAnalysisResult.value = '获取AI分析失败，请稍后再试'
      }
    }

    return {
      showAIResult,
      aiAnalysisResult,
      loading,
      tableData,
      formatValue,
      getChangeClass,
      exportData,
      showAIAnalysis,
      fetchPinzhiCashierData,
      Download,
      ChatDotSquare,
      Close,
      InfoFilled
    }
  }
}
</script>

<style scoped>
.pinzhi-tab {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h4 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.data-table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.pinzhi-table {
  border-radius: 4px;
  overflow: hidden;
}

.pinzhi-table :deep(.el-table__header-wrapper) {
  background: #fafafa;
}

.pinzhi-table :deep(.el-table__body-wrapper) {
  background: #fff;
}

.pinzhi-table :deep(.el-table__row) {
  background: #fff;
}

.pinzhi-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa;
}

.pinzhi-table :deep(.el-table__cell) {
  border-color: #e6e6e6;
  padding: 12px 8px;
}

.pinzhi-table :deep(.el-table__header .el-table__cell) {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.column-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #909399;
  cursor: pointer;
}

.info-icon:hover {
  color: #409eff;
}

.comparison-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.comparison-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.comparison-value {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  white-space: nowrap;
}

.comparison-value.increase {
  background: #e6f7ff;
  color: #1890ff;
  border: 2px solid #1890ff;
  font-weight: 600;
}

.comparison-value.decrease {
  background: #fff1f0;
  color: #ff4d4f;
  border: 2px solid #ff4d4f;
  font-weight: 600;
}

.comparison-value.no-change {
  background: #f6f6f6;
  color: #666;
  border: 2px solid #d9d9d9;
  font-weight: 500;
}

.field-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
}

.no-data {
  color: #999;
  font-style: italic;
  font-size: 12px;
  padding: 4px 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  display: inline-block;
}

.pinzhi-table :deep(th.el-table__cell) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
  text-align: center;
}

.pinzhi-table :deep(td.el-table__cell) {
  text-align: center;
}

.ai-analysis-card {
  margin-top: 20px;
}

.ai-analysis-content {
  padding: 20px;
}

.analysis-text {
  white-space: pre-line;
  line-height: 1.6;
  color: #666;
}
</style>