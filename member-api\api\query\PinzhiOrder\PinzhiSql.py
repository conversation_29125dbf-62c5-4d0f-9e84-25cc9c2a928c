"""
品智收银数据SQL查询模块
将每个SQL查询拆分成独立的函数，便于维护和独立管理
连接PostgreSQL数据库配置 - 品质收银数据库
"""

from typing import Optional, List, Dict, Any
import logging
import sys
import os

# 添加父目录到路径中以便导入MemberConsumeSql模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from MemberConsumeSql import MemberConsumeSqlQueries

logger = logging.getLogger(__name__)

class PinzhiCashierSqlQueries:
    """品智收银数据SQL查询类 - 每个查询都是独立的函数"""

    # ========== 品智收银基础数据查询 ==========

    @staticmethod
    def get_total_actual_revenue_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取营业额总实收查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            营业额总实收查询SQL
        """
        return f"""
        SELECT
            SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END)::numeric AS total_actual_revenue
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_total_expected_revenue_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取营业额总应收查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            营业额总应收查询SQL
        """
        return f"""
        SELECT
            SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_liushui, 0) ELSE 0 END)::numeric AS total_expected_revenue
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_discount_rate_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取折扣率查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            折扣率查询SQL
        """
        return f"""
        SELECT
            CASE
                WHEN SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_liushui, 0) ELSE 0 END) > 0
                THEN ROUND(
                    SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) /
                    SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_liushui, 0) ELSE 0 END),
                    4
                )
                ELSE NULL
            END AS discount_rate
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_dine_in_actual_revenue_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取堂食营业额实收查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            堂食营业额实收查询SQL
        """
        return f"""
        SELECT
            SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END)::numeric AS dine_in_actual_revenue
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_dine_in_order_count_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取堂食订单数查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            堂食订单数查询SQL
        """
        return f"""
        SELECT
            SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdi_tables, 0) ELSE 0 END) AS dine_in_order_count
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_takeout_actual_revenue_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取外卖营业额实收查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            外卖营业额实收查询SQL
        """
        return f"""
        SELECT
            SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END)::numeric AS takeout_actual_revenue
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_takeout_order_count_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取外卖订单数查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            外卖订单数查询SQL
        """
        return f"""
        SELECT
            SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdi_billcount, 0) ELSE 0 END) AS takeout_order_count
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_brand_avg_order_value_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取品牌平均客单价查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            品牌平均客单价查询SQL
        """
        return f"""
        SELECT
            CASE
                WHEN (SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdi_tables, 0) ELSE 0 END) +
                      SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdi_billcount, 0) ELSE 0 END)) > 0
                THEN ROUND(
                    SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) /
                    (SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdi_tables, 0) ELSE 0 END) +
                     SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdi_billcount, 0) ELSE 0 END)),
                    2
                )
                ELSE 0
            END AS brand_avg_order_value
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_dine_in_avg_order_value_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取堂食平均客单价查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            堂食平均客单价查询SQL
        """
        return f"""
        SELECT
            CASE
                WHEN SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdi_tables, 0) ELSE 0 END) > 0
                THEN ROUND(
                    SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) /
                    SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdi_tables, 0) ELSE 0 END),
                    2
                )
                ELSE 0
            END AS dine_in_avg_order_value
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_takeout_avg_order_value_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取外卖平均客单价查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            外卖平均客单价查询SQL
        """
        return f"""
        SELECT
            CASE
                WHEN SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdi_billcount, 0) ELSE 0 END) > 0
                THEN ROUND(
                    SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) /
                    SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdi_billcount, 0) ELSE 0 END),
                    2
                )
                ELSE 0
            END AS takeout_avg_order_value
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_dine_in_revenue_ratio_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取堂食实收占比查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            堂食实收占比查询SQL
        """
        return f"""
        SELECT
            CASE
                WHEN SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) > 0
                THEN ROUND(
                    SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) /
                    SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END),
                    4
                )
                ELSE 0
            END AS dine_in_revenue_ratio
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_takeout_revenue_ratio_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取外卖实收占比查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            外卖实收占比查询SQL
        """
        return f"""
        SELECT
            CASE
                WHEN SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) > 0
                THEN ROUND(
                    SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END) /
                    SUM(CASE WHEN p.fdi_businesstype IN (1, 2) THEN COALESCE(p.fdm_realamouont, 0) ELSE 0 END),
                    4
                )
                ELSE 0
            END AS takeout_revenue_ratio
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """

    @staticmethod
    def get_total_order_count_sql(brand_name: str, start_date: str, end_date: str) -> str:
        """获取订单总数查询SQL

        Args:
            brand_name: 品牌名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            订单总数查询SQL
        """
        return f"""
        SELECT
            (SUM(CASE WHEN p.fdi_businesstype = 1 THEN COALESCE(p.fdi_tables, 0) ELSE 0 END) +
             SUM(CASE WHEN p.fdi_businesstype = 2 THEN COALESCE(p.fdi_billcount, 0) ELSE 0 END)) AS total_order_count
        FROM {brand_name}.rept_ognperformance p
        WHERE p.fdd_paydate BETWEEN '{start_date}' AND '{end_date}'
        """


class PinzhiCashierCalculator:
    """品智收银数据计算器"""

    @staticmethod
    def calculate_discount_rate(actual_revenue: float, expected_revenue: float) -> float:
        """计算折扣率

        Args:
            actual_revenue: 实收金额
            expected_revenue: 应收金额

        Returns:
            折扣率（保留4位小数）
        """
        if expected_revenue > 0:
            return round(actual_revenue / expected_revenue, 4)
        return 0.0

    @staticmethod
    def format_discount_rate_percentage(discount_rate: float) -> str:
        """将折扣率格式化为百分比

        Args:
            discount_rate: 折扣率（如0.8411）

        Returns:
            百分比格式（如84.11%）
        """
        return f"{discount_rate * 100:.2f}%"

    # ========== 会员相关数据查询（复用会员消费模块函数）==========

    @staticmethod
    def get_member_total_actual_amount_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员总实收金额查询SQL

        会员总实收金额 = 会员实收现金金额 + 会员使用储值的实收金额
        复用会员消费模块的逻辑，确保数据一致性
        """
        # 转换日期格式：从YYYY-MM-DD转为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 构建条件
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        # 使用与MemberConsumeSql模块相同的逻辑
        return f"""
        SELECT
            COALESCE(
                (SUM(consume_cash) - SUM(cancel_cash)) +
                (SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount)),
                0
            ) AS member_total_actual_amount
        FROM dwoutput.dprpt_welife_consume_log
        WHERE ftime BETWEEN {start_date_formatted} AND {end_date_formatted}
          AND bid = {bid}
          {sid_condition}
        """

    @staticmethod
    def get_member_total_consume_count_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取会员总消费笔数查询SQL

        复用会员消费模块的函数，确保数据一致性
        """
        # 转换日期格式：从YYYY-MM-DD转为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 直接调用会员消费模块的函数
        return MemberConsumeSqlQueries.get_dwoutput_total_consume_pv_from_detail_sql(
            start_date_formatted, end_date_formatted, bid, sid
        )