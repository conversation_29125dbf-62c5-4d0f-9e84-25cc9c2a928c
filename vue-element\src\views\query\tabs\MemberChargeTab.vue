<template>
  <div class="member-charge-tab">
    <!-- 数据表格 -->
    <el-card class="data-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>会员充值数据</h4>
          <div class="table-actions">
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="success" size="small" @click="showAIAnalysis">
              <el-icon><ChatDotSquare /></el-icon>
              AI分析
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="tableData" stripe v-loading="loading" border class="member-table" :fit="true">
        <el-table-column label="字段名称" prop="fieldName" min-width="180" align="center">
          <template #default="scope">
            <div class="field-name">
              <span>{{ scope.row.fieldName }}</span>
              <el-tooltip :content="scope.row.calculationMethod" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="actualValue" label="实际值" min-width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.actualValue !== undefined && scope.row.actualValue !== null">{{ formatValue(scope.row.actualValue) }}{{ scope.row.unit }}</span>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
        <el-table-column label="环比对比" min-width="180" align="center">
          <template #header>
            <div class="column-header">
              <span>环比对比</span>
              <el-tooltip content="❗历史数据变化" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            <div class="comparison-cell" v-if="scope.row.chainComparison && scope.row.chainComparison.length > 0">
              <div class="comparison-row">
                <span :class="['comparison-value', getChangeClass(scope.row.chainChangeRate[0])]">
                  {{ formatValue(scope.row.chainComparison[0]) }}{{ scope.row.unit }}
                  <small v-if="scope.row.chainChangeRate[0] !== '-'">
                    ({{ scope.row.chainChangeRate[0] }})
                  </small>
                </span>
              </div>
            </div>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
        <el-table-column label="同比对比" min-width="180" align="center">
          <template #header>
            <div class="column-header">
              <span>同比对比</span>
              <el-tooltip content="❗与去年同期数据对比" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            <div class="comparison-cell" v-if="scope.row.yearOverYear !== undefined && scope.row.yearOverYear !== null">
              <div class="comparison-row">
                <span :class="['comparison-value', getChangeClass(scope.row.yearOverYearRate)]">
                  {{ formatValue(scope.row.yearOverYear) }}{{ scope.row.unit }}
                  <small v-if="scope.row.yearOverYearRate !== '-'">
                    ({{ scope.row.yearOverYearRate }})
                  </small>
                </span>
              </div>
            </div>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h4>充值留存分析</h4>
              <el-button type="text" size="small" @click="downloadChart('retention')">
                <el-icon><Download /></el-icon>
                下载图表
              </el-button>
            </div>
          </template>
          <div ref="retentionChart" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h4>充值vs消费对比</h4>
              <el-button type="text" size="small" @click="downloadChart('compare')">
                <el-icon><Download /></el-icon>
                下载图表
              </el-button>
            </div>
          </template>
          <div ref="compareChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- AI分析结果 -->
    <el-card v-if="showAIResult" class="ai-analysis-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>
            <el-icon><ChatDotSquare /></el-icon>
            AI智能分析
          </h4>
          <el-button type="text" size="small" @click="showAIResult = false">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
      <div class="ai-analysis-content">
        <el-alert title="分析结果" type="info" :closable="false">
          <div class="analysis-text">
            {{ aiAnalysisResult }}
          </div>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, ChatDotSquare, Close, InfoFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { MEMBER_CHARGE_MODULE } from '@/utils/constant'
import { memberReportApi } from '@/api/index'

export default {
  name: 'MemberChargeTab',
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update-loading'],
  setup(props, { emit }) {
    const retentionChart = ref(null)
    const compareChart = ref(null)
    const showAIResult = ref(false)
    const aiAnalysisResult = ref('')
    const loading = ref(false)
    const memberChargeData = ref({})
    const isRequesting = ref(false)  // 🔥 防止重复请求的标志位
    
    // 获取会员充值数据
    const fetchMemberChargeData = async () => {
      if (!props.queryParams || !props.queryParams.bid) {
        return
      }

      // 🔥 防止重复请求
      if (isRequesting.value) {
        console.log('MemberChargeTab: 请求正在进行中，跳过重复请求')
        return
      }

      console.log('MemberChargeTab: 开始获取充值数据')
      isRequesting.value = true
      loading.value = true
      emit('update-loading', true)

      // 🔥 显示查询提示
      ElMessage.info('正在查询会员充值数据，请稍候...')

      try {
        const response = await memberReportApi.getMemberChargeData(props.queryParams)
        console.log('MemberChargeTab - API响应:', response)
        
        if (response.code === 200) {
          memberChargeData.value = response.data || {}
          console.log('MemberChargeTab - 设置的数据:', memberChargeData.value)
        } else {
          ElMessage.error(response.message || '获取会员充值数据失败')
          memberChargeData.value = {}
        }
        
        // 重新初始化图表
        nextTick(() => {
          initRetentionChart()
          initCompareChart()
        })
        
      } catch (error) {
        console.error('获取会员充值数据失败:', error)
        ElMessage.error('获取会员充值数据失败，请重试')
        memberChargeData.value = {}
      } finally {
        loading.value = false
        emit('update-loading', false)
        isRequesting.value = false  // 🔥 重置请求标志位
        console.log('MemberChargeTab: 充值数据获取完成')
      }
    }
    
    // 监听查询参数变化
    // 移除 immediate: true，避免在数据恢复时自动触发查询
    // 只有用户主动操作时才应该触发查询
    watch(() => props.queryParams, (newParams) => {
      console.log('MemberChargeTab: 检测到查询参数变化')

      // 🔥 重要：如果新参数为null，说明正在清理数据
      if (!newParams) {
        console.log('检测到数据清理，清空充值数据')
        memberChargeData.value = {}
        isRequesting.value = false  // 重置请求状态
        return
      }

      if (newParams && newParams.bid) {
        console.log('检测到有效查询参数变化，准备获取新的充值数据')
        // 🔥 在获取新数据前先清空旧数据
        memberChargeData.value = {}

        // 🔥 添加延迟，避免与其他tab同时发起请求
        setTimeout(() => {
          fetchMemberChargeData()
        }, 100)  // 100ms延迟
      }
    }, { deep: true })
    
    // 表格数据
    const tableData = computed(() => {
      if (!memberChargeData.value || Object.keys(memberChargeData.value).length === 0) {
        return []
      }
      
      console.log('MemberChargeTab - 计算表格数据，当前数据:', memberChargeData.value)
      
      return Object.entries(MEMBER_CHARGE_MODULE).map(([fieldName, fieldConfig]) => {
        const key = fieldConfig.key
        const itemData = memberChargeData.value[key] || {}
        
        console.log(`MemberChargeTab - 字段 ${fieldName}(${key}):`, itemData)
        
        return {
          fieldName,
          actualValue: itemData.value || 0,
          unit: itemData.unit || '',
          chainComparison: itemData.chainComparison || [],
          chainChangeRate: itemData.chainChangeRate || [],
          chainLabels: itemData.chainLabels || [],
          yearOverYear: itemData.yearOverYear || 0,
          yearOverYearRate: itemData.yearOverYearRate || '0%',
          calculationMethod: fieldConfig.logic || ''
        }
      })
    })
    
    // 格式化数值
    const formatValue = (value) => {
      if (typeof value === 'number') {
        return value.toLocaleString()
      }
      return value
    }
    
    // 获取变化样式类
    const getChangeClass = (rate) => {
      if (rate === '-' || rate === '0%' || rate === '0.00%') return 'no-change'
      if (rate.includes('-')) return 'decrease'
      return 'increase'
    }
    
    // 初始化留存分析图表
    const initRetentionChart = () => {
      if (!retentionChart.value) return
      
      // 确保容器有宽度
      if (retentionChart.value.offsetWidth === 0) {
        setTimeout(initRetentionChart, 100)
        return
      }
      
      const chart = echarts.init(retentionChart.value, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })
      
      const chargeAmount = memberChargeData.value.chargeAmount || {}
      const consumePrepayAmount = memberChargeData.value.consumePrepayAmount || {}
      const retentionRate = memberChargeData.value.retentionRate || {}
      
      console.log('MemberChargeTab - 图表数据:', {
        chargeAmount,
        consumePrepayAmount,
        retentionRate
      })

      // 构建数据系列
      const periods = ['上期', '本期']
      const chargeAmountData = [
        chargeAmount.chainComparison?.[0] || 0,
        chargeAmount.value || 0
      ]
      const consumePrepayAmountData = [
        consumePrepayAmount.chainComparison?.[0] || 0,
        consumePrepayAmount.value || 0
      ]
      const retentionRateData = [
        retentionRate.chainComparison?.[0] || 0,
        retentionRate.value || 0
      ]
      
      const option = {
        title: {
          text: '充值留存分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.seriesName === '留存率') {
                result += param.seriesName + ': ' + param.value + '%<br/>'
              } else {
                result += param.seriesName + ': ' + param.value.toLocaleString() + '元<br/>'
              }
            })
            return result
          }
        },
        legend: {
          data: ['充值金额', '消耗金额', '留存率'],
          bottom: 10,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: periods,
          axisLabel: {
            fontSize: 12,
            color: '#333'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '金额(元)',
            axisLabel: {
              formatter: '{value}元',
              color: '#333'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#eee'
              }
            }
          },
          {
            type: 'value',
            name: '留存率(%)',
            axisLabel: {
              formatter: '{value}%',
              color: '#333'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '充值金额',
            type: 'bar',
            data: chargeAmountData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#52c41a' },
                { offset: 1, color: '#73d13d' }
              ])
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}元'
            }
          },
          {
            name: '消耗金额',
            type: 'bar',
            data: consumePrepayAmountData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#ff4d4f' },
                { offset: 1, color: '#ff7875' }
              ])
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}元'
            }
          },
          {
            name: '留存率',
            type: 'line',
            yAxisIndex: 1,
            data: retentionRateData,
            lineStyle: {
              width: 3,
              color: '#1890ff'
            },
            itemStyle: {
              color: '#1890ff'
            },
            symbolSize: 8,
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%'
            }
          }
        ]
      }
      chart.setOption(option)
      
      // 强制重绘
      chart.resize()
    }
    
    // 初始化对比图表
    const initCompareChart = () => {
      if (!compareChart.value) return
      
      // 确保容器有宽度
      if (compareChart.value.offsetWidth === 0) {
        setTimeout(initCompareChart, 100)
        return
      }
      
      const chart = echarts.init(compareChart.value, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })
      
      const chargeAmount = memberChargeData.value.chargeAmount || {}
      const chargeCount = memberChargeData.value.chargeCount || {}
      
      console.log('MemberChargeTab - 对比图表数据:', {
        chargeAmount,
        chargeCount
      })

      // 构建数据系列
      const periods = ['上期', '本期']
      const chargeAmountData = [
        chargeAmount.chainComparison?.[0] || 0,
        chargeAmount.value || 0
      ]
      const chargeCountData = [
        chargeCount.chainComparison?.[0] || 0,
        chargeCount.value || 0
      ]
      
      const option = {
        title: {
          text: '充值vs消费对比',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.seriesName === '充值笔数') {
                result += param.seriesName + ': ' + param.value + '笔<br/>'
              } else {
                result += param.seriesName + ': ' + param.value.toLocaleString() + '元<br/>'
              }
            })
            return result
          }
        },
        legend: {
          data: ['充值金额', '充值笔数'],
          bottom: 10,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: periods,
          axisLabel: {
            fontSize: 12,
            color: '#333'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '金额(元)',
            axisLabel: {
              formatter: '{value}元',
              color: '#333'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#eee'
              }
            }
          },
          {
            type: 'value',
            name: '笔数',
            axisLabel: {
              formatter: '{value}笔',
              color: '#333'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '充值金额',
            type: 'bar',
            data: chargeAmountData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#faad14' },
                { offset: 1, color: '#ffd666' }
              ])
            },
            barWidth: '40%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}元'
            }
          },
          {
            name: '充值笔数',
            type: 'line',
            yAxisIndex: 1,
            data: chargeCountData,
            lineStyle: {
              width: 3,
              color: '#722ed1'
            },
            itemStyle: {
              color: '#722ed1'
            },
            symbolSize: 8,
            label: {
              show: true,
              position: 'top',
              formatter: '{c}笔'
            }
          }
        ]
      }
      chart.setOption(option)
      
      // 强制重绘
      chart.resize()
    }
    
    // 导出数据
    const exportData = () => {
      ElMessage.success('数据导出功能开发中...')
    }
    
    // 显示AI分析
    const showAIAnalysis = async () => {
      if (!props.queryParams || !props.queryParams.bid) {
        ElMessage.warning('请先设置查询条件')
        return
      }
      
      showAIResult.value = true
      aiAnalysisResult.value = '正在分析中，请稍候...'
      
      try {
        const response = await memberReportApi.getMemberChargeAIAnalysis(props.queryParams)
        if (response.code === 200) {
          aiAnalysisResult.value = response.data
        } else {
          aiAnalysisResult.value = response.message || 'AI分析失败'
        }
      } catch (error) {
        console.error('获取AI分析失败:', error)
        aiAnalysisResult.value = '获取AI分析失败，请稍后再试'
      }
    }
    
    // 下载图表
    const downloadChart = (type) => {
      ElMessage.success(`${type}图表下载功能开发中...`)
    }
    
    // 处理窗口大小变化
    const handleResize = () => {
      // 重新初始化图表
      initRetentionChart()
      initCompareChart()
    }
    
    // 初始化图表
    onMounted(() => {
      nextTick(() => {
        // 监听窗口大小变化
        window.addEventListener('resize', handleResize)
      })
    })
    
    // 在组件销毁时清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      
      if (retentionChart.value) {
        const chart = echarts.getInstanceByDom(retentionChart.value)
        if (chart) chart.dispose()
      }
      
      if (compareChart.value) {
        const chart = echarts.getInstanceByDom(compareChart.value)
        if (chart) chart.dispose()
      }
    })
    
    return {
      retentionChart,
      compareChart,
      showAIResult,
      aiAnalysisResult,
      loading,
      tableData,
      formatValue,
      getChangeClass,
      exportData,
      showAIAnalysis,
      downloadChart,
      fetchMemberChargeData,
      Download,
      ChatDotSquare,
      Close,
      InfoFilled
    }
  }
}
</script>

<style scoped>
.member-charge-tab {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h4 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.data-table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.member-table {
  border-radius: 4px;
  overflow: hidden;
}

.member-table :deep(.el-table__header-wrapper) {
  background: #fafafa;
}

.member-table :deep(.el-table__body-wrapper) {
  background: #fff;
}

.member-table :deep(.el-table__row) {
  background: #fff;
}

.member-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa;
}

.member-table :deep(.el-table__cell) {
  border-color: #e6e6e6;
  padding: 12px 8px;
}

.member-table :deep(.el-table__header .el-table__cell) {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.column-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #909399;
  cursor: pointer;
}

.info-icon:hover {
  color: #409eff;
}

.comparison-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.comparison-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.comparison-labels {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.label-text {
  font-size: 11px;
  color: #909399;
  padding: 2px 6px;
  background: #f5f7fa;
  border-radius: 3px;
  border: 1px solid #e8e8e8;
  white-space: nowrap;
  font-weight: 500;
}

.comparison-value {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  white-space: nowrap;
}

.comparison-value.increase {
  background: #e6f7ff;
  color: #1890ff;
  border: 2px solid #1890ff;
  font-weight: 600;
}

.comparison-value.decrease {
  background: #fff1f0;
  color: #ff4d4f;
  border: 2px solid #ff4d4f;
  font-weight: 600;
}

.comparison-value.no-change {
  background: #f6f6f6;
  color: #666;
  border: 2px solid #d9d9d9;
  font-weight: 500;
}

.field-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
}

.no-data {
  color: #999;
  font-style: italic;
  font-size: 12px;
  padding: 4px 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  display: inline-block;
}

.member-table :deep(th.el-table__cell) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
  text-align: center;
}

.member-table :deep(td.el-table__cell) {
  text-align: center;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 450px;
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 380px;
}

.ai-analysis-card {
  margin-top: 20px;
}

.ai-analysis-content {
  padding: 20px;
}

.analysis-text {
  white-space: pre-line;
  line-height: 1.6;
  color: #666;
}
</style> 