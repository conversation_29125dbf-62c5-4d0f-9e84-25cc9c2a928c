<template>
  <div class="member-base-tab">
    <!-- 数据表格 -->
    <el-card class="data-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>会员基础数据</h4>
          <div class="table-actions">
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="success" size="small" @click="showAIAnalysis">
              <el-icon><ChatDotSquare /></el-icon>
              AI分析
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="tableData" stripe v-loading="loading" border class="member-table" :fit="true">
        <el-table-column label="字段名称" prop="fieldName" min-width="180" align="center">
          <template #default="scope">
            <div class="field-name">
              <span>{{ scope.row.fieldName }}</span>
              <el-tooltip :content="scope.row.calculationMethod" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="actualValue" label="实际值" min-width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.actualValue !== undefined && scope.row.actualValue !== null">{{ scope.row.actualValue }}{{ scope.row.unit }}</span>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
        <el-table-column label="环比对比" min-width="180" align="center">
          <template #header>
            <div class="column-header">
              <span>环比对比</span>
              <el-tooltip content="❗历史数据变化" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            <div class="comparison-cell" v-if="scope.row.chainComparison && scope.row.chainComparison.length > 0">
              <div class="comparison-row">
                <span :class="['comparison-value', getChangeClass(scope.row.chainChangeRate[0])]">
                  {{ scope.row.chainComparison[0] }}{{ scope.row.unit }}
                  <small v-if="scope.row.chainChangeRate[0] !== '-'">
                    ({{ scope.row.chainChangeRate[0] }})
                  </small>
                </span>
              </div>
            </div>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
        <el-table-column label="同比对比" min-width="180" align="center">
          <template #header>
            <div class="column-header">
              <span>同比对比</span>
              <el-tooltip content="❗与去年同期数据对比" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            <div class="comparison-cell" v-if="scope.row.yearOverYear !== undefined && scope.row.yearOverYear !== null">
              <div class="comparison-row">
                <span :class="['comparison-value', getChangeClass(scope.row.yearOverYearRate)]">
                  {{ scope.row.yearOverYear }}{{ scope.row.unit }}
                  <small v-if="scope.row.yearOverYearRate !== '-'">
                    ({{ scope.row.yearOverYearRate }})
                  </small>
                </span>
              </div>
            </div>
            <span v-else class="no-data">未查询到当前数据</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h4>会员增长趋势</h4>
              <el-button type="text" size="small" @click="downloadChart('growth')">
                <el-icon><Download /></el-icon>
                下载图表
              </el-button>
            </div>
          </template>
          <div ref="growthChart" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h4>会员构成分析</h4>
              <el-button type="text" size="small" @click="downloadChart('composition')">
                <el-icon><Download /></el-icon>
                下载图表
              </el-button>
            </div>
          </template>
          <div ref="compositionChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- AI分析结果 -->
    <el-card v-if="showAIResult" class="ai-analysis-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>
            <el-icon><ChatDotSquare /></el-icon>
            AI智能分析
          </h4>
          <el-button type="text" size="small" @click="showAIResult = false">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
      <div class="ai-analysis-content">
        <el-alert title="分析结果" type="info" :closable="false">
          <div class="analysis-text">
            {{ aiAnalysisResult }}
          </div>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, ChatDotSquare, Close, InfoFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { MEMBER_BASE_MODULE } from '@/utils/constant'
import { memberReportApi } from '@/api/index'

export default {
  name: 'MemberBaseTab',
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update-loading'],
  setup(props, { emit }) {
    const growthChart = ref(null)
    const compositionChart = ref(null)
    const showAIResult = ref(false)
    const aiAnalysisResult = ref('')
    const loading = ref(false)
    const memberBaseData = ref({})
    
    // 获取会员基础数据
    const fetchMemberBaseData = async () => {
      console.log('=== MemberBaseTab: 开始获取全新数据 ===')
      console.log('传入的查询参数:', props.queryParams)

      if (!props.queryParams || !props.queryParams.bid) {
        console.log('查询参数无效，跳过数据获取')
        return
      }

      // 🔥 确保在开始获取新数据前，本地数据已清空
      console.log('清空本地数据，准备获取全新数据...')
      memberBaseData.value = {}

      loading.value = true
      emit('update-loading', true)
      
      try {
        console.log('发送API请求...')
        const response = await memberReportApi.getMemberBaseData(props.queryParams)
        console.log('API响应:', response)

        memberBaseData.value = response.data || {}
        console.log('设置memberBaseData:', memberBaseData.value)
        
        // 重新初始化图表
        nextTick(() => {
          initGrowthChart()
          initCompositionChart()
        })
        
      } catch (error) {
        console.error('获取会员基础数据失败:', error)
        ElMessage.error('获取会员基础数据失败，请重试')
      } finally {
        loading.value = false
        emit('update-loading', false)
        console.log('=== MemberBaseTab: 数据获取结束 ===')
      }
    }
    
    // 监听查询参数变化
    // 移除 immediate: true，避免在数据恢复时自动触发查询
    // 只有用户主动操作时才应该触发查询
    watch(() => props.queryParams, (newParams, oldParams) => {
      console.log('MemberBaseTab: 检测到查询参数变化')
      console.log('新参数:', newParams)
      console.log('旧参数:', oldParams)

      // 🔥 重要：如果新参数为null，说明正在清理数据
      if (!newParams) {
        console.log('检测到数据清理，清空本地数据')
        memberBaseData.value = {}
        return
      }

      if (newParams && newParams.bid) {
        console.log('检测到有效查询参数变化，准备获取新数据')
        // 🔥 在获取新数据前先清空旧数据
        memberBaseData.value = {}
        fetchMemberBaseData()
      }
    }, { deep: true })
    
    // 表格数据
    const tableData = computed(() => {
      if (!memberBaseData.value || Object.keys(memberBaseData.value).length === 0) {
        return []
      }
      
      return Object.entries(MEMBER_BASE_MODULE).map(([fieldName, fieldConfig]) => {
        const key = fieldConfig.key
        const itemData = memberBaseData.value[key] || {}
        
        return {
          fieldName,
          actualValue: itemData.value || 0,
          unit: itemData.unit || '',
          chainComparison: itemData.chainComparison || [],
          chainChangeRate: itemData.chainChangeRate || [],
          chainLabels: itemData.chainLabels || [],
          yearOverYear: itemData.yearOverYear || 0,
          yearOverYearRate: itemData.yearOverYearRate || '0%',
          calculationMethod: fieldConfig.logic || ''
        }
      })
    })
    
    // 获取变化样式类
    const getChangeClass = (rate) => {
      if (rate === '-' || rate === '0%' || rate === '0.00%') return 'no-change'
      if (rate.includes('-')) return 'decrease'
      return 'increase'
    }
    
    // 初始化会员增长趋势图表
    const initGrowthChart = () => {
      if (!growthChart.value) return
      
      // 确保容器有宽度
      if (growthChart.value.offsetWidth === 0) {
        setTimeout(initGrowthChart, 100)
        return
      }
      
      const chart = echarts.init(growthChart.value, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })
      
      const totalMembers = memberBaseData.value.totalMembers || {}
      const newMembers = memberBaseData.value.newMembers || {}
      const unfollowMembers = memberBaseData.value.unfollowMembers || {}
      const newUnfollowMembers = memberBaseData.value.newUnfollowMembers || {} // 新增取关人数
      
      console.log('MemberBaseTab - 增长趋势图表数据:', {
        totalMembers,
        newMembers,
        unfollowMembers,
        newUnfollowMembers // 新增取关人数
      })

      // 构建数据系列
      const periods = ['上期', '本期']
      const totalMembersData = [
        totalMembers.chainComparison?.[0] || 0,
        totalMembers.value || 0
      ]
      const newMembersData = [
        newMembers.chainComparison?.[0] || 0,
        newMembers.value || 0
      ]
      const unfollowMembersData = [
        unfollowMembers.chainComparison?.[0] || 0,
        unfollowMembers.value || 0
      ]
      const newUnfollowMembersData = [ // 新增取关人数
        newUnfollowMembers.chainComparison?.[0] || 0,
        newUnfollowMembers.value || 0
      ]
      
      const option = {
        title: {
          text: '会员增长趋势',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              result += param.seriesName + ': ' + param.value + '人<br/>'
            })
            return result
          }
        },
        legend: {
          data: ['会员总数', '新增会员', '取关会员', '新增取关人数'], // 添加新增取关人数
          bottom: 10,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: periods,
          axisLabel: {
            fontSize: 12,
            color: '#333'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '人数',
          axisLabel: {
            formatter: '{value}人',
            color: '#333'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          }
        },
        series: [
          {
            name: '会员总数',
            type: 'bar',
            data: totalMembersData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1890ff' },
                { offset: 1, color: '#40a9ff' }
              ])
            },
            barWidth: '30%',
            label: {
              show: false // 隐藏数字
            }
          },
          {
            name: '新增会员',
            type: 'line',
            data: newMembersData,
            lineStyle: {
              width: 3,
              color: '#52c41a'
            },
            itemStyle: {
              color: '#52c41a'
            },
            symbolSize: 8,
            label: {
              show: false // 隐藏数字
            }
          },
          {
            name: '取关会员',
            type: 'line',
            data: unfollowMembersData,
            lineStyle: {
              width: 3,
              color: '#ff4d4f'
            },
            itemStyle: {
              color: '#ff4d4f'
            },
            symbolSize: 8,
            label: {
              show: false // 隐藏数字
            }
          },
          {
            name: '新增取关人数', // 新增取关人数
            type: 'line',
            data: newUnfollowMembersData,
            lineStyle: {
              width: 3,
              color: '#faad14'
            },
            itemStyle: {
              color: '#faad14'
            },
            symbolSize: 8,
            label: {
              show: false // 隐藏数字
            }
          }
        ]
      }
      chart.setOption(option)
      
      // 强制重绘
      chart.resize()
    }
    
    // 初始化会员构成分析图表
    const initCompositionChart = () => {
      if (!compositionChart.value) return
      
      // 确保容器有宽度
      if (compositionChart.value.offsetWidth === 0) {
        setTimeout(initCompositionChart, 100)
        return
      }
      
      const chart = echarts.init(compositionChart.value, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })
      
      const totalConsumeMembers = memberBaseData.value.totalConsumeMembers || {}
      const totalChargeMembers = memberBaseData.value.totalChargeMembers || {}
      const totalCompleteMembers = memberBaseData.value.totalCompleteMembers || {}
      const consumeZeroMembers = memberBaseData.value.consumeZeroMembers || {} // 会员0次消费总人数
      
      console.log('MemberBaseTab - 构成分析图表数据:', {
        totalConsumeMembers,
        totalChargeMembers,
        totalCompleteMembers,
        consumeZeroMembers // 会员0次消费总人数
      })

      // 构建数据系列
      const periods = ['上期', '本期']
      const totalConsumeMembersData = [
        totalConsumeMembers.chainComparison?.[0] || 0,
        totalConsumeMembers.value || 0
      ]
      const totalChargeMembersData = [
        totalChargeMembers.chainComparison?.[0] || 0,
        totalChargeMembers.value || 0
      ]
      const totalCompleteMembersData = [
        totalCompleteMembers.chainComparison?.[0] || 0,
        totalCompleteMembers.value || 0
      ]
      
      const option = {
        title: {
          text: '会员构成分析',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              result += param.seriesName + ': ' + param.value + '人<br/>'
            })
            return result
          }
        },
        legend: {
          data: ['消费会员', '储值会员', '完善资料会员'],
          bottom: 10,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: periods,
          axisLabel: {
            fontSize: 12,
            color: '#333'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '人数',
          axisLabel: {
            formatter: '{value}人',
            color: '#333'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          }
        },
        series: [
          {
            name: '消费会员',
            type: 'bar',
            data: totalConsumeMembersData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1890ff' },
                { offset: 1, color: '#40a9ff' }
              ])
            },
            barWidth: '25%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}人'
            }
          },
          {
            name: '储值会员',
            type: 'bar',
            data: totalChargeMembersData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#52c41a' },
                { offset: 1, color: '#73d13d' }
              ])
            },
            barWidth: '25%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}人'
            }
          },
          {
            name: '完善资料会员',
            type: 'bar',
            data: totalCompleteMembersData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#faad14' },
                { offset: 1, color: '#ffc53d' }
              ])
            },
            barWidth: '25%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}人'
            }
          }
        ]
      }
      chart.setOption(option)
      
      // 强制重绘
      chart.resize()
    }
    
    // 导出数据
    const exportData = () => {
      ElMessage.success('数据导出功能开发中...')
    }
    
    // 显示AI分析
    const showAIAnalysis = async () => {
      if (!props.queryParams || !props.queryParams.bid) {
        ElMessage.warning('请先设置查询条件')
        return
      }
      
      showAIResult.value = true
      aiAnalysisResult.value = '正在分析中，请稍候...'
      
      try {
        const response = await memberReportApi.getMemberBaseAIAnalysis(props.queryParams)
        if (response.code === 200) {
          aiAnalysisResult.value = response.data
        } else {
          aiAnalysisResult.value = response.message || 'AI分析失败'
        }
      } catch (error) {
        console.error('获取AI分析失败:', error)
        aiAnalysisResult.value = '获取AI分析失败，请稍后再试'
      }
    }
    
    // 下载图表
    const downloadChart = (type) => {
      ElMessage.success(`${type}图表下载功能开发中...`)
    }
    
    // 处理窗口大小变化
    const handleResize = () => {
      // 重新初始化图表
      initGrowthChart()
      initCompositionChart()
    }
    
    // 初始化图表
    onMounted(() => {
      nextTick(() => {
        // 监听窗口大小变化
        window.addEventListener('resize', handleResize)
      })
    })
    
    // 在组件销毁时清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      
      if (growthChart.value) {
        const chart = echarts.getInstanceByDom(growthChart.value)
        if (chart) chart.dispose()
      }
      
      if (compositionChart.value) {
        const chart = echarts.getInstanceByDom(compositionChart.value)
        if (chart) chart.dispose()
      }
    })
    
    return {
      growthChart,
      compositionChart,
      showAIResult,
      aiAnalysisResult,
      loading,
      tableData,
      getChangeClass,
      exportData,
      showAIAnalysis,
      downloadChart,
      fetchMemberBaseData,
      Download,
      ChatDotSquare,
      Close,
      InfoFilled
    }
  }
}
</script>

<style scoped>
.member-base-tab {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h4 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.data-table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.member-table {
  border-radius: 4px;
  overflow: hidden;
}

.member-table :deep(.el-table__header-wrapper) {
  background: #fafafa;
}

.member-table :deep(.el-table__body-wrapper) {
  background: #fff;
}

.member-table :deep(.el-table__row) {
  background: #fff;
}

.member-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa;
}

.member-table :deep(.el-table__cell) {
  border-color: #e6e6e6;
  padding: 12px 8px;
}

.member-table :deep(.el-table__header .el-table__cell) {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.column-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #909399;
  cursor: pointer;
}

.info-icon:hover {
  color: #409eff;
}

.comparison-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.comparison-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.comparison-labels {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.label-text {
  font-size: 11px;
  color: #909399;
  padding: 2px 6px;
  background: #f5f7fa;
  border-radius: 3px;
  border: 1px solid #e8e8e8;
  white-space: nowrap;
  font-weight: 500;
}

.comparison-value {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  white-space: nowrap;
}

.comparison-value.increase {
  background: #e6f7ff;
  color: #1890ff;
  border: 2px solid #1890ff;
  font-weight: 600;
}

.comparison-value.decrease {
  background: #fff1f0;
  color: #ff4d4f;
  border: 2px solid #ff4d4f;
  font-weight: 600;
}

.comparison-value.no-change {
  background: #f6f6f6;
  color: #666;
  border: 2px solid #d9d9d9;
  font-weight: 500;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 450px;
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 380px;
}

.ai-analysis-card {
  margin-top: 20px;
}

.ai-analysis-content {
  padding: 20px;
}

.analysis-text {
  white-space: pre-line;
  line-height: 1.6;
  color: #666;
}

.field-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
}

.no-data {
  color: #999;
  font-style: italic;
  font-size: 12px;
  padding: 4px 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
}
</style> 