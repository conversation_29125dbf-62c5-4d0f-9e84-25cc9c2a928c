# -*- coding: utf-8 -*-
"""
PPT图片保存和管理模块
基于bid+日期的动态图片存储策略
"""

import os
import datetime
import shutil
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class PPTImageManager:
    """PPT图片管理器 - 基于bid+日期的存储策略"""

    def __init__(self, bid: str, base_path: str = "uploads/temp"):
        """
        初始化图片管理器

        Args:
            bid: 品牌ID
            base_path: 基础存储路径（相对于工作目录）
        """
        self.bid = str(bid).strip()
        self.base_path = Path(base_path)
        self.date_str = datetime.datetime.now().strftime("%Y%m%d")
        self.session_dir = self.base_path / f"{self.bid}_{self.date_str}"

        # 确保目录存在
        self._ensure_directory()

        logger.info(f"初始化图片管理器 - bid: {self.bid}, 会话目录: {self.session_dir}")

    def _ensure_directory(self) -> bool:
        """确保会话目录存在"""
        try:
            self.session_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建/确认图片目录: {self.session_dir}")
            return True
        except Exception as e:
            logger.error(f"创建图片目录失败: {e}")
            return False

    def get_image_path(self, image_type: str, extension: str = "png") -> str:
        """
        获取指定类型图片的完整路径

        Args:
            image_type: 图片类型 (如: test, logo, chart, trend, revenue, coupon)
            extension: 文件扩展名

        Returns:
            str: 图片完整路径
        """
        filename = f"{image_type}.{extension}"
        full_path = self.session_dir / filename
        logger.debug(f"生成图片路径: {image_type} -> {full_path}")
        return str(full_path)

    def get_all_image_params(self) -> Dict[str, str]:
        """
        获取所有图片参数的路径映射（仅包含现有的6种图片类型）

        Returns:
            Dict: 图片参数字典，key为image_xxx格式
        """
        # 现有的图片类型，包含新增的会员图片和消费图片
        existing_image_types = ["test", "logo", "chart", "trend", "revenue", "coupon",
                               "new_member_add_last_year", "new_member_add_this_year",
                               "member_consumption_last_year", "member_consumption_this_year",
                               "avg_consumption_last_year", "avg_consumption_this_year",
                               "consumption_num_last_year", "consumption_num_this_year",
                               "member_charge_last_year", "member_charge_this_year"]

        image_params = {}
        for img_type in existing_image_types:
            param_key = f"image_{img_type}"
            image_params[param_key] = self.get_image_path(img_type)

        logger.info(f"生成图片参数映射，共 {len(image_params)} 个参数")
        return image_params

    def save_image(self, image_data: Any, image_type: str, save_func: callable = None) -> str:
        """
        保存图片到会话目录

        Args:
            image_data: 图片数据
            image_type: 图片类型
            save_func: 自定义保存函数

        Returns:
            str: 保存的图片路径，失败返回空字符串
        """
        try:
            image_path = self.get_image_path(image_type)

            if save_func:
                # 使用提供的保存函数
                save_func(image_data, image_path)
            else:
                # 默认保存逻辑（占位符）
                self._default_save_placeholder(image_path, image_type)

            if os.path.exists(image_path):
                logger.info(f"图片保存成功: {image_type} -> {image_path}")
                return image_path
            else:
                logger.error(f"图片保存失败: {image_path}")
                return ""

        except Exception as e:
            logger.error(f"保存图片失败 {image_type}: {e}")
            return ""

    def _default_save_placeholder(self, save_path: str, image_type: str):
        """
        创建默认占位符图片

        Args:
            save_path: 保存路径
            image_type: 图片类型
        """
        try:
            # 创建一个简单的占位符文件
            Path(save_path).touch()
            logger.info(f"创建占位符图片: {save_path}")

        except Exception as e:
            logger.error(f"创建占位符图片失败: {e}")

    def cleanup_old_sessions(self, days: int = 7) -> int:
        """
        清理过期的会话目录

        Args:
            days: 保留天数

        Returns:
            int: 清理的目录数量
        """
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
            cutoff_str = cutoff_date.strftime("%Y%m%d")

            cleaned_count = 0

            if not self.base_path.exists():
                return 0

            for item in self.base_path.iterdir():
                if item.is_dir() and "_" in item.name:
                    try:
                        # 提取日期部分
                        parts = item.name.split("_")
                        if len(parts) >= 2:
                            date_part = parts[-1]  # 取最后一部分作为日期

                            # 验证日期格式并比较
                            if len(date_part) == 8 and date_part.isdigit() and date_part < cutoff_str:
                                shutil.rmtree(item)
                                logger.info(f"清理过期图片目录: {item}")
                                cleaned_count += 1

                    except Exception as e:
                        logger.warning(f"清理目录失败 {item}: {e}")

            logger.info(f"清理完成，共清理 {cleaned_count} 个过期目录")
            return cleaned_count

        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            return 0

    def get_session_info(self) -> Dict[str, Any]:
        """
        获取当前会话信息

        Returns:
            Dict: 会话信息
        """
        return {
            "bid": self.bid,
            "date": self.date_str,
            "session_dir": str(self.session_dir),
            "dir_exists": self.session_dir.exists(),
            "image_count": len(list(self.session_dir.glob("*.png"))) if self.session_dir.exists() else 0
        }


def create_image_manager(bid: str) -> PPTImageManager:
    """
    创建图片管理器实例的工厂函数

    Args:
        bid: 品牌ID

    Returns:
        PPTImageManager: 图片管理器实例
    """
    return PPTImageManager(bid)


def cleanup_all_expired_sessions(days: int = 7) -> int:
    """
    清理所有过期的图片会话

    Args:
        days: 保留天数

    Returns:
        int: 清理的目录总数
    """
    base_path = Path("uploads/temp")
    if not base_path.exists():
        return 0

    # 创建一个临时管理器来执行清理
    temp_manager = PPTImageManager("temp")
    return temp_manager.cleanup_old_sessions(days)