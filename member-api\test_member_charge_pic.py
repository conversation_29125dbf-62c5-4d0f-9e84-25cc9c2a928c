#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会员充值消费图片生成测试脚本
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_member_charge_pic():
    """测试会员充值消费图片生成功能"""
    try:
        # 导入必要的模块
        from api.PPTreport.picture.MemberChargePic import create_member_charge_pic_generator
        from api.PPTreport.picture.PictureSave import PictureSaveManager
        
        logger.info("开始测试会员充值消费图片生成...")
        
        # 创建测试参数
        test_params = {
            'bid': 'test_brand_001',
            'sid': None,
            'end_date': '2024-12-31'
        }
        
        # 创建图片管理器
        session_dir = project_root / "test_output" / "member_charge_test"
        session_dir.mkdir(parents=True, exist_ok=True)
        
        image_manager = PictureSaveManager(str(session_dir))
        
        # 创建会员充值消费图片生成器
        generator = create_member_charge_pic_generator(test_params['bid'], image_manager)
        
        logger.info(f"测试参数: {test_params}")
        logger.info(f"输出目录: {session_dir}")
        
        # 生成图片
        result = await generator.generate_member_charge_charts(test_params)
        
        logger.info(f"生成结果: {result}")
        
        # 检查生成的文件
        for key, path in result.items():
            if path and os.path.exists(path):
                file_size = os.path.getsize(path)
                logger.info(f"✓ {key}: {path} (大小: {file_size} 字节)")
            else:
                logger.warning(f"✗ {key}: 文件不存在或路径为空")
        
        logger.info("会员充值消费图片生成测试完成")
        return result
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

async def test_integration():
    """测试完整集成功能"""
    try:
        from api.PPTreport.PicAcquisition import pic_acquisition_service
        
        logger.info("开始测试完整集成功能...")
        
        # 创建测试参数
        test_params = {
            'bid': 'test_brand_001',
            'sid': None,
            'end_date': '2024-12-31'
        }
        
        # 测试图片路径获取
        result = await pic_acquisition_service.get_picture_paths(test_params)
        
        logger.info(f"集成测试结果包含 {len(result)} 个参数")
        
        # 检查会员充值消费相关的参数
        charge_keys = [
            'image_member_charge_last_year',
            'image_member_charge_this_year',
            'member_charge_last_year_analysis_report',
            'member_charge_this_year_analysis_report'
        ]
        
        for key in charge_keys:
            if key in result:
                logger.info(f"✓ {key}: {result[key][:100]}...")
            else:
                logger.warning(f"✗ {key}: 未找到")
        
        logger.info("完整集成测试完成")
        return result
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("会员充值消费图片生成测试")
    logger.info("=" * 60)
    
    # 运行测试
    asyncio.run(test_member_charge_pic())
    
    logger.info("\n" + "=" * 60)
    logger.info("完整集成测试")
    logger.info("=" * 60)
    
    # 运行集成测试
    asyncio.run(test_integration())
    
    logger.info("\n测试完成！")
