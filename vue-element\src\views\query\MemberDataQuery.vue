<template>
  <div class="member-data-query">
    <!-- 查询表单 -->
    <el-card class="query-form-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>
            <el-icon><Search /></el-icon>
            会员数据查询
          </h3>
        </div>
      </template>
      
      <el-form ref="queryForm" :model="queryParams" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="查询类型" prop="queryType">
              <el-select v-model="queryParams.queryType" placeholder="请选择查询类型" @change="handleQueryTypeChange">
                <el-option label="周分析" value="week" />
                <el-option label="月分析" value="month" />
                <el-option label="季度分析" value="quarter" />
                <el-option label="半年分析" value="halfyear" />
                <el-option label="自定义查询" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="品牌ID" prop="bid">
              <el-input v-model="queryParams.bid" placeholder="请输入品牌ID（必填）" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="门店ID" prop="sid">
              <el-input v-model="queryParams.sid" placeholder="请输入门店ID（选填）" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="queryParams.startDate"
                type="date"
                placeholder="请选择开始日期"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8" v-if="queryParams.queryType === 'custom'">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="queryParams.endDate"
                type="date"
                placeholder="请选择结束日期"
                :disabled-date="disabledEndDate"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：品智收银筛选条件 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="收银系统" prop="cashierSystem">
              <el-select v-model="queryParams.cashierSystem" placeholder="请选择收银系统" @change="handleCashierSystemChange">
                <el-option label="0-无收银系统" value="0" />
                <el-option label="1-品智收银" value="1" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="queryParams.cashierSystem === '1'">
            <el-form-item label="商户选择" prop="merchantId">
              <el-select
                v-model="queryParams.merchantId"
                placeholder="请选择商户"
                filterable
                clearable
              >
                <el-option
                  v-for="store in storeOptions"
                  :key="store.value"
                  :label="store.label"
                  :value="store.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="handleQuery" :loading="loading">
                <el-icon><Search /></el-icon>
                开始查询
              </el-button>
              <el-button @click="handleReset">
                <el-icon><RefreshRight /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 日期限制提示 -->
      <div v-if="dateLimit" class="date-limit-tip">
        <el-alert :title="dateLimit.label" type="info" :closable="false" show-icon />
      </div>
    </el-card>

    <!-- 查询结果 -->
    <div v-if="hasData" class="query-results">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <h3>
              <el-icon><DataLine /></el-icon>
              查询结果
            </h3>
            <div class="query-info">
              <el-tag type="info">{{ queryTypeLabel }}</el-tag>
              <el-tag type="success">{{ formatDate(queryParams.startDate) }} 开始</el-tag>
              <el-tag v-if="queryParams.endDate" type="warning">{{ formatDate(queryParams.endDate) }} 结束</el-tag>
            </div>
          </div>
        </template>
        
        <!-- 数据展示Tab -->
        <el-tabs v-model="activeTab" class="data-tabs">
          <el-tab-pane label="会员基础信息" name="memberBase">
            <MemberBaseTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
          
          <el-tab-pane label="会员消费信息" name="memberConsume">
            <MemberConsumeTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
          
          <el-tab-pane label="会员充值信息" name="memberCharge">
            <MemberChargeTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
          
          <el-tab-pane label="券交易信息" name="couponTrade">
            <CouponTradeTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>

          <el-tab-pane label="品智收银信息" name="pinzhiCashier" v-if="queryParams.cashierSystem === '1' && hasData">
            <PinzhiTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
    
    <!-- 空状态 -->
    <el-empty
      v-else
      :image-size="200"
      description="请先选择查询条件并点击<开始查询>按钮"
    >
      <template #image>
        <el-icon size="200" color="#c0c4cc">
          <Search />
        </el-icon>
      </template>
    </el-empty>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, RefreshRight, DataLine } from '@element-plus/icons-vue'
import SampleData, { SampleDataGenerator } from '@/data/sampleData'
import { getStoreOptions } from '@/utils/PinzhiStoreMapping'
import MemberBaseTab from './tabs/MemberBaseTab.vue'
import MemberConsumeTab from './tabs/MemberConsumeTab.vue'
import MemberChargeTab from './tabs/MemberChargeTab.vue'
import CouponTradeTab from './tabs/CouponTradeTab.vue'
import PinzhiTab from './tabs/PinzhiTab.vue'

export default {
  name: 'MemberDataQuery',
  components: {
    MemberBaseTab,
    MemberConsumeTab,
    MemberChargeTab,
    CouponTradeTab,
    PinzhiTab
  },
  setup() {
    const loading = ref(false)
    const hasData = ref(false)
    const activeTab = ref('memberBase')
    const dateLimit = ref(null)
    const isRestoringData = ref(false)  // 标志位：是否正在恢复数据

    // localStorage 键名
    const STORAGE_KEYS = {
      queryParams: 'member_query_params',
      queryResults: 'member_query_results',
      activeTab: 'member_active_tab'
    }

    // localStorage 工具函数
    const saveToStorage = (key, data) => {
      try {
        localStorage.setItem(key, JSON.stringify(data))
      } catch (error) {
        console.warn('保存数据到localStorage失败:', error)
      }
    }

    const loadFromStorage = (key) => {
      try {
        const data = localStorage.getItem(key)
        return data ? JSON.parse(data) : null
      } catch (error) {
        console.warn('从localStorage读取数据失败:', error)
        return null
      }
    }

    const clearStorage = (key) => {
      try {
        localStorage.removeItem(key)
      } catch (error) {
        console.warn('清除localStorage数据失败:', error)
      }
    }
    
    // 查询参数
    const queryParams = reactive({
      queryType: 'week',
      bid: '',
      sid: '',
      startDate: null,
      endDate: null,
      cashierSystem: '0',  // 默认无收银系统
      merchantId: ''       // 商户ID
    })

    // 门店选项
    const storeOptions = ref(getStoreOptions())
    
    // 当前查询参数（用于传递给子组件）
    const currentQueryParams = ref(null)
    
    // 数据存储
    const memberBaseData = ref(null)
    const memberConsumeData = ref(null)
    const memberChargeData = ref(null)
    const couponTradeData = ref(null)
    const pinzhiData = ref(null)
    
    // 表单验证规则
    const rules = {
      queryType: [
        { required: true, message: '请选择查询类型', trigger: 'change' }
      ],
      bid: [
        { required: true, message: '请输入品牌ID', trigger: 'blur' }
      ],
      startDate: [
        { required: true, message: '请选择开始日期', trigger: 'change' }
      ]
    }
    
    // 查询类型标签
    const queryTypeLabel = computed(() => {
      const labels = {
        week: '周分析',
        month: '月分析',
        quarter: '季度分析',
        halfyear: '半年分析',
        custom: '自定义查询'
      }
      return labels[queryParams.queryType] || '未知类型'
    })
    
    // 更新loading状态
    const updateLoading = (value) => {
      loading.value = value
    }
    
    // 日期禁用逻辑
    const disabledDate = (time) => {
      if (!queryParams.queryType) return true
      
      const limit = SampleData.dateLimits(queryParams.queryType)
      return time.getTime() > limit.maxDate.getTime() || time.getTime() < limit.minDate.getTime()
    }
    
    // 结束日期禁用逻辑
    const disabledEndDate = (time) => {
      if (!queryParams.startDate) return true
      return time.getTime() < queryParams.startDate.getTime()
    }
    
    // 处理查询类型变化
    const handleQueryTypeChange = (value, preserveDates = false) => {
      // 如果不是保留日期模式，才重置日期
      if (!preserveDates) {
        queryParams.startDate = null
        queryParams.endDate = null
      }

      // 更新日期限制
      dateLimit.value = SampleData.dateLimits(value)
    }

    // 处理收银系统变化
    const handleCashierSystemChange = (value) => {
      // 当切换收银系统时，清空商户选择
      queryParams.merchantId = ''

      // 如果切换到品智收银，可以在这里做一些初始化操作
      if (value === '1') {
        console.log('切换到品智收银系统')
      } else {
        console.log('切换到无收银系统')
      }
    }
    
    // 处理查询
    const handleQuery = async () => {
      console.log('=== 开始查询 ===')
      console.log('查询参数:', queryParams)
      
      // 验证必填字段
      if (!queryParams.bid) {
        console.log('验证失败: 缺少品牌ID')
        ElMessage.error('请输入品牌ID')
        return
      }
      
      if (!queryParams.startDate) {
        console.log('验证失败: 缺少开始日期')
        ElMessage.error('请选择开始日期')
        return
      }
      
      // 品智收银系统验证
      if (queryParams.cashierSystem === '1') {
        if (!queryParams.merchantId) {
          console.log('验证失败: 选择品智收银时必须选择商户')
          ElMessage.error('选择品智收银时必须选择商户')
          return
        }
      }

      console.log('验证通过，开始清理旧数据并构建查询数据')

      // 🔥 重要：在开始新查询前，先清空所有之前的数据
      console.log('清理之前的查询数据...')
      hasData.value = false
      currentQueryParams.value = null
      memberBaseData.value = null
      memberConsumeData.value = null
      memberChargeData.value = null
      couponTradeData.value = null
      pinzhiData.value = null

      // 清除localStorage中的旧查询结果
      clearStorage(STORAGE_KEYS.queryResults)

      loading.value = true

      // 🔥 根据查询类型显示不同的提示信息
      if (queryParams.cashierSystem === '1') {
        ElMessage.info('正在清理旧数据，准备获取品智收银数据，数据量较大可能需要1-2分钟...')
      } else {
        ElMessage.info('正在清理旧数据，准备获取新数据...')
      }

      try {
        // 构建查询参数
        const queryData = {
          query_type: queryParams.queryType,
          bid: queryParams.bid,
          sid: queryParams.sid || null,
          start_date: queryParams.startDate ? formatDateForAPI(queryParams.startDate) : null,
          end_date: queryParams.endDate ? formatDateForAPI(queryParams.endDate) : null,
          compare_options: ['chain', 'yearOnYear'], // 默认启用环比和同比
          cashier_system: queryParams.cashierSystem || '0',
          merchant_id: queryParams.merchantId || null
        }
        
        // 如果不是自定义查询，根据查询类型计算结束日期
        if (queryParams.queryType !== 'custom') {
          const startDate = new Date(queryParams.startDate)
          let endDate = new Date(startDate)
          
          switch (queryParams.queryType) {
            case 'week':
              // 周：从选择的日期开始，持续7天
              endDate.setDate(startDate.getDate() + 6)
              break
            case 'month':
              // 月：从选择的日期开始，到下个月的同一天的前一天
              endDate.setMonth(startDate.getMonth() + 1)
              endDate.setDate(startDate.getDate() - 1)
              break
            case 'quarter':
              // 季度：从选择的日期开始，到第3个月后的同一天的前一天
              endDate.setMonth(startDate.getMonth() + 3)
              endDate.setDate(startDate.getDate() - 1)
              break
            case 'halfyear':
              // 半年：从选择的日期开始，到第6个月后的同一天的前一天
              endDate.setMonth(startDate.getMonth() + 6)
              endDate.setDate(startDate.getDate() - 1)
              break
          }
          
          queryData.end_date = formatDateForAPI(endDate)
        }
        
        console.log('构建的查询数据:', queryData)
        
        // 设置当前查询参数
        currentQueryParams.value = queryData
        console.log('查询参数已设置，开始生成新数据...')

        // 🔥 确保数据完全清空后再生成新数据
        // 为其他tab生成样本数据（暂时保留）
        console.log('生成会员消费数据...')
        memberConsumeData.value = SampleDataGenerator.generateMemberConsumeData(queryParams.queryType)

        console.log('生成会员充值数据...')
        memberChargeData.value = SampleDataGenerator.generateMemberChargeData(queryParams.queryType)

        console.log('生成优惠券交易数据...')
        couponTradeData.value = SampleDataGenerator.generateCouponTradeData()

        // 只有选择了品智收银系统才生成品智数据
        if (queryParams.cashierSystem === '1') {
          console.log('生成品智数据...')
          pinzhiData.value = SampleDataGenerator.generatePinzhiData ?
            SampleDataGenerator.generatePinzhiData(queryParams.queryType) :
            { message: '品智数据生成器未实现' }
        } else {
          console.log('跳过品智数据生成（未选择品智收银系统）')
          pinzhiData.value = null
        }

        // 🔥 重要：只有在所有新数据都生成完成后，才设置hasData为true
        hasData.value = true
        console.log('所有新数据生成完成，数据区域现在显示全新的查询结果')

        // 保存查询结果到localStorage
        const queryResults = {
          queryParams: queryData,
          memberBaseData: memberBaseData.value,
          memberConsumeData: memberConsumeData.value,
          memberChargeData: memberChargeData.value,
          couponTradeData: couponTradeData.value,
          pinzhiData: pinzhiData.value,
          activeTab: activeTab.value,
          hasData: true,
          queryTime: new Date().toISOString()
        }
        saveToStorage(STORAGE_KEYS.queryResults, queryResults)

        console.log('查询参数设置完成，数据区域已显示全新的查询结果')

        // 🔥 根据查询类型显示不同的成功消息
        if (queryParams.cashierSystem === '1') {
          ElMessage.success('✅ 品智收银数据查询完成！现在显示的是全新的查询结果')
        } else {
          ElMessage.success('✅ 数据已完全更新！现在显示的是全新的查询结果')
        }
        
      } catch (error) {
        console.error('查询失败:', error)
        ElMessage.error('查询失败：' + error.message)
      } finally {
        loading.value = false
        console.log('=== 查询结束 ===')
      }
    }
    
    // 重置表单
    const handleReset = () => {
      console.log('=== 开始重置表单和数据 ===')

      // 重置查询参数
      queryParams.queryType = 'week'
      queryParams.bid = ''
      queryParams.sid = ''
      queryParams.startDate = null
      queryParams.endDate = null
      queryParams.cashierSystem = '0'
      queryParams.merchantId = ''

      // 🔥 重要：清空所有数据
      currentQueryParams.value = null
      hasData.value = false
      memberBaseData.value = null
      memberConsumeData.value = null
      memberChargeData.value = null
      couponTradeData.value = null
      pinzhiData.value = null
      dateLimit.value = null

      // 清除localStorage
      clearStorage(STORAGE_KEYS.queryParams)
      clearStorage(STORAGE_KEYS.queryResults)

      console.log('表单和数据重置完成')
      ElMessage.success('✅ 表单已重置，所有数据已清空')
    }
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    }

    // 格式化日期为API所需格式 (YYYY-MM-DD)
    const formatDateForAPI = (date) => {
      if (!date) return null
      
      // 避免时区转换问题，使用本地时间
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    }
    
    // 数据恢复和持久化
    onMounted(() => {
      console.log('数据分析页面加载，尝试恢复数据...')

      // 设置数据恢复标志位，防止触发不必要的响应式更新
      isRestoringData.value = true

      try {
        // 恢复查询参数
        const savedQueryParams = loadFromStorage(STORAGE_KEYS.queryParams)
        if (savedQueryParams) {
          console.log('恢复查询参数:', savedQueryParams)
          Object.assign(queryParams, {
            queryType: savedQueryParams.queryType || 'week',
            bid: savedQueryParams.bid || '',
            sid: savedQueryParams.sid || '',
            startDate: savedQueryParams.startDate ? new Date(savedQueryParams.startDate) : null,
            endDate: savedQueryParams.endDate ? new Date(savedQueryParams.endDate) : null,
            cashierSystem: savedQueryParams.cashierSystem || '0',
            merchantId: savedQueryParams.merchantId || null
          })

          // 恢复查询参数后，更新日期限制但保留已恢复的日期
          handleQueryTypeChange(queryParams.queryType, true)
          ElMessage.success('已恢复上次的查询条件')
        }

        // 恢复查询结果数据
        const savedResults = loadFromStorage(STORAGE_KEYS.queryResults)
        if (savedResults) {
          // 检查数据是否过期（24小时）
          const queryTime = new Date(savedResults.queryTime)
          const now = new Date()
          const hoursDiff = (now - queryTime) / (1000 * 60 * 60)

          if (hoursDiff < 24) {
            console.log('恢复查询结果数据:', savedResults)

            // 恢复数据
            currentQueryParams.value = savedResults.queryParams
            memberBaseData.value = savedResults.memberBaseData
            memberConsumeData.value = savedResults.memberConsumeData
            memberChargeData.value = savedResults.memberChargeData
            couponTradeData.value = savedResults.couponTradeData
            pinzhiData.value = savedResults.pinzhiData
            activeTab.value = savedResults.activeTab || 'memberBase'
            hasData.value = savedResults.hasData || false

            ElMessage.success('已恢复上次的查询数据')
          } else {
            console.log('查询数据已过期，清除缓存')
            clearStorage(STORAGE_KEYS.queryResults)
            // 如果没有恢复查询参数，才设置默认查询类型
            if (!savedQueryParams) {
              handleQueryTypeChange('week')
            }
          }
        } else {
          // 如果没有保存的数据且没有恢复查询参数，才设置默认查询类型
          if (!savedQueryParams) {
            handleQueryTypeChange('week')
          }
        }

        // 恢复活跃标签页
        const savedActiveTab = loadFromStorage(STORAGE_KEYS.activeTab)
        if (savedActiveTab) {
          activeTab.value = savedActiveTab
        }

      } catch (error) {
        console.error('数据恢复过程中发生错误:', error)
        ElMessage.warning('数据恢复失败，将使用默认设置')
        // 发生错误时设置默认查询类型
        handleQueryTypeChange('week')
      } finally {
        // 无论成功还是失败，都要重置数据恢复标志位
        isRestoringData.value = false
        console.log('数据恢复完成，重置标志位')
      }
    })

    // 监听查询参数变化，自动保存
    watch(queryParams, (newParams) => {
      // 如果正在恢复数据，不执行保存操作，避免不必要的触发
      if (isRestoringData.value) {
        console.log('正在恢复数据，跳过参数保存')
        return
      }
      console.log('查询参数变化，自动保存:', newParams)
      saveToStorage(STORAGE_KEYS.queryParams, newParams)
    }, { deep: true })

    // 监听活跃标签页变化，自动保存
    watch(activeTab, (newTab) => {
      console.log('活跃标签页变化，自动保存:', newTab)
      saveToStorage(STORAGE_KEYS.activeTab, newTab)
    })

    return {
      loading,
      hasData,
      activeTab,
      queryParams,
      currentQueryParams,
      rules,
      dateLimit,
      storeOptions,
      memberBaseData,
      memberConsumeData,
      memberChargeData,
      couponTradeData,
      pinzhiData,
      queryTypeLabel,
      updateLoading,
      disabledDate,
      disabledEndDate,
      handleQueryTypeChange,
      handleCashierSystemChange,
      handleQuery,
      handleReset,
      formatDate,
      Search,
      RefreshRight,
      DataLine
    }
  }
}
</script>

<style scoped>
.member-data-query {
  padding: 20px;
  min-height: 100vh;
  overflow: auto;
  width: 100%;
}

.query-form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
}

.query-info {
  display: flex;
  gap: 8px;
}

.date-limit-tip {
  margin-top: 16px;
}

.query-results {
  margin-top: 20px;
  width: 100%;
}

.data-tabs {
  margin-top: 20px;
}

.data-tabs :deep(.el-tabs__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
}

.data-tabs :deep(.el-tabs__nav) {
  background: transparent;
}

.data-tabs :deep(.el-tabs__item) {
  color: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
}

.data-tabs :deep(.el-tabs__item.is-active) {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.data-tabs :deep(.el-tabs__content) {
  padding: 20px;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: none;
}

/* 确保图表容器有足够的宽度 */
.data-tabs :deep(.el-tab-pane) {
  width: 100%;
  min-width: 800px;
}

/* 确保图表行有足够的空间 */
.data-tabs :deep(.chart-section) {
  width: 100%;
  margin: 0;
  padding: 0 10px;
}

/* 确保图表列有足够的空间 */
.data-tabs :deep(.chart-section .el-col) {
  padding: 0 10px;
}

/* 确保图表卡片有足够的空间 */
.data-tabs :deep(.chart-card) {
  width: 100%;
  margin-bottom: 20px;
}
</style> 