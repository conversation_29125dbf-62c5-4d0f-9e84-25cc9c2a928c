# -*- coding: utf-8 -*-
"""
会员等级消费分析AI分析器
提供会员等级消费数据的智能分析功能
"""

import logging
from typing import Dict, Any, List

from services.llm_service import LLMService
from .LevelConsumptionPromt import LevelConsumptionAnalysisPrompts

logger = logging.getLogger(__name__)

class LevelConsumptionAiAnalyzer:
    """会员等级消费分析AI分析器"""

    def __init__(self):
        """初始化会员等级消费分析AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员等级消费分析AI分析器初始化完成")

    async def analyze_level_consumption_data(self, level_data: List[Dict[str, Any]]) -> str:
        """
        分析会员等级消费数据

        Args:
            level_data: 会员等级消费数据列表

        Returns:
            str: AI分析结果（100-150字的整段分析）
        """
        try:
            if not level_data:
                return "会员等级消费数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员消费数据的完整性和准确性，为精准营销和会员价值提升提供可靠的数据支撑。"

            # 生成分析提示词
            analysis_prompt = LevelConsumptionAnalysisPrompts.get_level_consumption_analysis_prompt(level_data)

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的会员运营数据分析师，请基于以下会员等级消费数据进行深入分析：

数据概况：
{self._format_level_consumption_data_summary(level_data)}

分析要求：
请生成一段100-150字的专业分析报告，要求：
1. 整段输出，不要分点或分段
2. 重点关注：等级间消费差异、会员价值分层、运营优化建议
3. 避免简单的数据描述，要提供深度洞察和实操建议
4. 确保建议具体可执行，避免空泛的建议
5. 语言简洁专业，逻辑清晰连贯
6. 字数控制在100-150字之间

基础分析框架：
{analysis_prompt}

请生成专业的会员等级消费分析报告：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                # 确保结果长度在合理范围内
                result = analysis_result.strip()
                if len(result) > 200:
                    # 如果太长，截取前150字并确保句子完整
                    result = result[:150]
                    last_period = result.rfind('。')
                    if last_period > 100:
                        result = result[:last_period + 1]

                logger.info(f"会员等级消费数据AI分析完成，分析长度: {len(result)}字")
                return result
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"会员等级消费数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return LevelConsumptionAnalysisPrompts.get_fallback_analysis()

    def _format_level_consumption_data_summary(self, level_data: List[Dict[str, Any]]) -> str:
        """
        格式化会员等级消费数据摘要

        Args:
            level_data: 会员等级消费数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not level_data:
            return "无数据"

        try:
            # 提取数据
            level_names = [item.get('ccName', '未知等级') for item in level_data]
            per_capita_values = [item.get('perCapitaConsumption', 0) for item in level_data]
            unit_price_values = [item.get('customerUnitPrice', 0) for item in level_data]
            frequency_values = [item.get('avgConsumFrequency', 0) for item in level_data]

            # 计算统计指标
            total_levels = len(level_data)
            avg_per_capita = sum(per_capita_values) / total_levels if total_levels > 0 else 0
            avg_unit_price = sum(unit_price_values) / total_levels if total_levels > 0 else 0
            avg_frequency = sum(frequency_values) / total_levels if total_levels > 0 else 0

            # 找出极值
            max_per_capita = max(per_capita_values) if per_capita_values else 0
            min_per_capita = min(per_capita_values) if per_capita_values else 0
            max_unit_price = max(unit_price_values) if unit_price_values else 0
            min_unit_price = min(unit_price_values) if unit_price_values else 0
            max_frequency = max(frequency_values) if frequency_values else 0
            min_frequency = min(frequency_values) if frequency_values else 0

            # 找出对应的等级名称
            max_per_capita_level = level_names[per_capita_values.index(max_per_capita)] if per_capita_values else "未知"
            max_unit_price_level = level_names[unit_price_values.index(max_unit_price)] if unit_price_values else "未知"
            max_frequency_level = level_names[frequency_values.index(max_frequency)] if frequency_values else "未知"

            summary = f"""
等级数量：{total_levels}个会员等级
等级名称：{', '.join(level_names)}
人均消费额：平均{avg_per_capita:.2f}元，最高{max_per_capita:.2f}元({max_per_capita_level})，最低{min_per_capita:.2f}元
客单价：平均{avg_unit_price:.2f}元，最高{max_unit_price:.2f}元({max_unit_price_level})，最低{min_unit_price:.2f}元
消费频次：平均{avg_frequency:.2f}次，最高{max_frequency:.2f}次({max_frequency_level})，最低{min_frequency:.2f}次
等级差异：人均消费额差距{max_per_capita - min_per_capita:.2f}元，客单价差距{max_unit_price - min_unit_price:.2f}元，频次差距{max_frequency - min_frequency:.2f}次
详细数据：{[f"{level_names[i]}({per_capita_values[i]:.2f}/{unit_price_values[i]:.2f}/{frequency_values[i]:.2f})" for i in range(len(level_names))]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化会员等级消费数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"


# 创建全局分析器实例
level_consumption_ai_analyzer = LevelConsumptionAiAnalyzer()